# Microchip AI Chatbot Extension - Installation Guide

## Quick Start

### Step 1: Install the Extension

1. **Download** the `microchip-ai-chatbot-1.0.0.vsix` file
2. **Open VS Code**
3. **Press** `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
4. **Type** "Extensions: Install from VSIX..."
5. **Select** the downloaded `.vsix` file
6. **Restart** VS Code when prompted

### Step 2: Start the Backend Server

The extension needs a backend server to work with the Microchip AI API.

1. **Open Terminal** in the extension directory
2. **Run** the server:
   ```bash
   npm run server
   ```
3. **Keep the terminal open** - the server must stay running

### Step 3: Get Your API Key

1. **Visit** the Microchip AI platform website
2. **Sign up** or log in to your account
3. **Generate** an API key from your dashboard
4. **Copy** the API key

### Step 4: Use the Extension

1. **Look for** the Microchip AI Chatbot icon in the VS Code sidebar (Explorer panel)
2. **Click** the icon to open the chat
3. **Enter** your API key when prompted
4. **Click** "Connect" to test the connection
5. **Start chatting** with the AI assistant!

## Detailed Instructions

### Installing from VSIX File

The VSIX file (`microchip-ai-chatbot-1.0.0.vsix`) contains the complete extension package.

**Method 1: Command Palette**
1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Type "Extensions: Install from VSIX..."
4. Press Enter
5. Navigate to the VSIX file and select it
6. Wait for installation to complete
7. Restart VS Code

**Method 2: Extensions View**
1. Open VS Code
2. Click the Extensions icon in the sidebar (or press `Ctrl+Shift+X`)
3. Click the "..." menu in the Extensions view
4. Select "Install from VSIX..."
5. Navigate to the VSIX file and select it
6. Wait for installation to complete
7. Restart VS Code

### Setting Up the Backend Server

The backend server is required because web browsers block direct requests to the Microchip AI API due to CORS (Cross-Origin Resource Sharing) restrictions.

**Prerequisites:**
- Node.js (version 16 or higher)
- npm (comes with Node.js)

**Starting the Server:**
1. Open a terminal/command prompt
2. Navigate to the extension directory
3. Run: `npm install` (only needed the first time)
4. Run: `npm run server`
5. You should see: "🚀 Microchip AI Proxy Server running on port 3001"

**Keep the server running** while using the extension. You can minimize the terminal window, but don't close it.

### Getting Your Microchip AI API Key

1. **Visit** the Microchip AI platform (check Microchip's official website for the current URL)
2. **Create an account** if you don't have one
3. **Log in** to your account
4. **Navigate** to the API section or developer dashboard
5. **Generate** a new API key
6. **Copy** the API key - you'll need it for the extension

**Important:** Keep your API key secure and don't share it with others.

### First-Time Setup in VS Code

After installing the extension and starting the server:

1. **Find the Extension**: Look for the Microchip AI Chatbot icon in the VS Code sidebar (usually in the Explorer panel)

2. **Open the Chat**: Click the icon to open the chat interface

3. **Enter API Key**: 
   - You'll see an API key input screen
   - Paste your Microchip AI API key
   - Click the eye icon to show/hide the key as you type
   - Click "Connect" to test the connection

4. **Start Chatting**: 
   - Once connected, you'll see the chat interface
   - Type your questions about Microchip products
   - The AI will respond with helpful information

### Troubleshooting Installation

**Extension doesn't appear after installation:**
- Restart VS Code completely
- Check the Extensions panel to ensure it's enabled
- Look for any error messages in the VS Code output panel

**Server won't start:**
- Make sure Node.js is installed: `node --version`
- Make sure you're in the correct directory
- Run `npm install` first if you haven't already
- Check if port 3001 is already in use

**Can't connect to API:**
- Verify your API key is correct
- Make sure the backend server is running
- Check your internet connection
- Ensure no firewall is blocking the connection

**Chat interface doesn't load:**
- Check the VS Code Developer Console (Help > Toggle Developer Tools)
- Look for any error messages
- Try restarting VS Code

### Uninstalling

To remove the extension:
1. Open VS Code
2. Go to Extensions panel (`Ctrl+Shift+X`)
3. Find "Microchip AI Chatbot"
4. Click the gear icon and select "Uninstall"
5. Restart VS Code

## Support

If you encounter issues:
1. Check this troubleshooting guide
2. Ensure all prerequisites are met
3. Verify the backend server is running
4. Check the VS Code Developer Console for errors

For additional help, refer to the main README file or create an issue in the project repository.
